VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(3, 1, 45, 0),
    prodvers=(3, 1, 45, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        '040904b0',
        [StringStruct('CompanyName', 'PyWxDump'),
        StringStruct('FileDescription', 'PyWxDump from https://github.com/xaoyaoo/PyWxDump'),
        StringStruct('FileVersion', '3.1.45'),
        StringStruct('InternalName', 'wxdump.exe'),
        StringStruct('LegalCopyright', 'Copyright (C) http://github.com/xaoyaoo/PyWxDump. All rights reserved'),
        StringStruct('OriginalFilename', 'wxdump.exe'),
        StringStruct('ProductName', 'wxdump'),
        StringStruct('ProductVersion', '3.1.45'),
        StringStruct('SquirrelAwareVersion', '1')])
      ]), 
    VarFileInfo([VarStruct('Translation',  [2052, 1200])])
  ]
)