{"name": "wxdump_web", "version": "2.4.10", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force"}, "dependencies": {"@types/axios": "^0.14.0", "axios": "^1.6.3", "cors": "^2.8.5", "echarts": "^5.5.0", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.4.4", "markdown-it": "^14.0.0", "v3-infinite-loading": "^1.3.1", "vue": "^3.3.11", "vue-echarts": "^6.6.9", "vue-router": "^4.2.5", "vue3-markdown-it": "^1.0.10"}, "devDependencies": {"@tsconfig/node18": "^18.2.2", "@types/markdown-it": "^13.0.7", "@types/node": "^18.19.3", "@vitejs/plugin-vue": "^4.5.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/tsconfig": "^0.5.0", "npm-run-all2": "^6.1.1", "sass": "^1.69.7", "typescript": "~5.3.0", "vite": "^5.0.10", "vue-tsc": "^1.8.25"}}