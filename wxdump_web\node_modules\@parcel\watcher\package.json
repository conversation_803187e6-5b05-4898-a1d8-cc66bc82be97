{"_from": "@parcel/watcher@^2.4.1", "_id": "@parcel/watcher@2.5.1", "_inBundle": false, "_integrity": "sha512-dfUnCxiN9H4ap84DvD2ubjw+3vUNpstxa0TneY/Paat8a3R4uQZDLSvWjmznAY/DoahqTHl9V46HF/Zs3F29pg==", "_location": "/@parcel/watcher", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@parcel/watcher@^2.4.1", "name": "@parcel/watcher", "escapedName": "@parcel%2fwatcher", "scope": "@parcel", "rawSpec": "^2.4.1", "saveSpec": null, "fetchSpec": "^2.4.1"}, "_requiredBy": ["/sass"], "_resolved": "https://registry.npmmirror.com/@parcel/watcher/-/watcher-2.5.1.tgz", "_shasum": "342507a9cfaaf172479a882309def1e991fb1200", "_spec": "@parcel/watcher@^2.4.1", "_where": "D:\\codes\\Godspeed\\Date\\2025-07-31 微信安卓插件\\wxdump_web\\node_modules\\sass", "binary": {"napi_versions": [3]}, "bugs": {"url": "https://github.com/parcel-bundler/watcher/issues"}, "bundleDependencies": false, "dependencies": {"@parcel/watcher-android-arm64": "2.5.1", "@parcel/watcher-darwin-arm64": "2.5.1", "@parcel/watcher-darwin-x64": "2.5.1", "@parcel/watcher-freebsd-x64": "2.5.1", "@parcel/watcher-linux-arm-glibc": "2.5.1", "@parcel/watcher-linux-arm-musl": "2.5.1", "@parcel/watcher-linux-arm64-glibc": "2.5.1", "@parcel/watcher-linux-arm64-musl": "2.5.1", "@parcel/watcher-linux-x64-glibc": "2.5.1", "@parcel/watcher-linux-x64-musl": "2.5.1", "@parcel/watcher-win32-arm64": "2.5.1", "@parcel/watcher-win32-ia32": "2.5.1", "@parcel/watcher-win32-x64": "2.5.1", "detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0"}, "deprecated": false, "description": "A native C++ Node module for querying and subscribing to filesystem events. Used by Parcel 2.", "devDependencies": {"esbuild": "^0.19.8", "fs-extra": "^10.0.0", "husky": "^7.0.2", "lint-staged": "^11.1.2", "mocha": "^9.1.1", "napi-wasm": "^1.1.0", "prebuildify": "^6.0.1", "prettier": "^2.3.2"}, "engines": {"node": ">= 10.0.0"}, "files": ["index.js", "index.js.flow", "index.d.ts", "wrapper.js", "package.json", "README.md", "LICENSE", "src", "scripts/build-from-source.js", "binding.gyp"], "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "homepage": "https://github.com/parcel-bundler/watcher#readme", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "license": "MIT", "lint-staged": {"*.{js,json,md}": ["prettier --write", "git add"]}, "main": "index.js", "name": "@parcel/watcher", "optionalDependencies": {"@parcel/watcher-android-arm64": "2.5.1", "@parcel/watcher-darwin-arm64": "2.5.1", "@parcel/watcher-darwin-x64": "2.5.1", "@parcel/watcher-freebsd-x64": "2.5.1", "@parcel/watcher-linux-arm-glibc": "2.5.1", "@parcel/watcher-linux-arm-musl": "2.5.1", "@parcel/watcher-linux-arm64-glibc": "2.5.1", "@parcel/watcher-linux-arm64-musl": "2.5.1", "@parcel/watcher-linux-x64-glibc": "2.5.1", "@parcel/watcher-linux-x64-musl": "2.5.1", "@parcel/watcher-win32-arm64": "2.5.1", "@parcel/watcher-win32-ia32": "2.5.1", "@parcel/watcher-win32-x64": "2.5.1"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/parcel-bundler/watcher.git"}, "scripts": {"build": "node-gyp rebuild", "format": "prettier --write \"./**/*.{js,json,md}\"", "install": "node scripts/build-from-source.js", "prebuild": "prebuildify --napi --strip --tag-libc", "test": "mocha"}, "types": "index.d.ts", "version": "2.5.1"}