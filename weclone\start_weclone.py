#!/usr/bin/env python3
"""
WeClone 项目启动脚本
提供简单的命令行界面来启动不同的服务
"""

import os
import sys
import subprocess
import argparse

def check_model():
    """检查模型是否存在"""
    model_path = "./chatglm3-6b"
    if not os.path.exists(model_path):
        print("❌ 模型目录不存在，请先下载模型")
        return False
    
    # 检查关键模型文件
    key_files = ["config.json", "tokenizer_config.json"]
    for file in key_files:
        if not os.path.exists(os.path.join(model_path, file)):
            print(f"❌ 模型文件 {file} 不存在")
            return False
    
    print("✅ 模型文件检查通过")
    return True

def start_api_service():
    """启动 API 服务"""
    print("🚀 启动 API 服务...")
    if not check_model():
        return
    
    os.chdir("src")
    try:
        subprocess.run([sys.executable, "api_service.py"], check=True)
    except KeyboardInterrupt:
        print("\n⏹️ API 服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ API 服务启动失败: {e}")

def start_web_demo():
    """启动 Web 演示"""
    print("🌐 启动 Web 演示...")
    if not check_model():
        return
    
    os.chdir("src")
    try:
        subprocess.run([sys.executable, "web_demo.py"], check=True)
    except KeyboardInterrupt:
        print("\n⏹️ Web 演示已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ Web 演示启动失败: {e}")

def start_training():
    """启动训练"""
    print("🎯 启动模型训练...")
    if not check_model():
        return
    
    # 检查训练数据
    if not os.path.exists("./data/res_csv/sft/sft-my.json"):
        print("❌ 训练数据不存在，请先运行数据预处理")
        print("运行: python make_dataset/csv_to_json.py")
        return
    
    os.chdir("src")
    try:
        subprocess.run([sys.executable, "train_sft.py"], check=True)
    except KeyboardInterrupt:
        print("\n⏹️ 训练已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 训练启动失败: {e}")

def test_model():
    """测试模型"""
    print("🧪 测试模型...")
    if not check_model():
        return
    
    os.chdir("src")
    try:
        subprocess.run([sys.executable, "test_model.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 模型测试失败: {e}")

def process_data():
    """处理数据"""
    print("📊 处理聊天数据...")
    try:
        subprocess.run([sys.executable, "make_dataset/csv_to_json.py"], check=True)
        print("✅ 数据处理完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 数据处理失败: {e}")

def download_model():
    """下载模型"""
    print("📥 下载 ChatGLM3-6B 模型...")
    print("⚠️ 模型大小约 12GB，请确保网络稳定")
    
    try:
        # 设置环境变量使用魔搭社区
        os.environ["USE_MODELSCOPE_HUB"] = "1"
        subprocess.run(["git", "lfs", "install"], check=True)
        subprocess.run([
            "git", "clone", 
            "https://www.modelscope.cn/ZhipuAI/chatglm3-6b.git"
        ], check=True)
        print("✅ 模型下载完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 模型下载失败: {e}")

def main():
    parser = argparse.ArgumentParser(description="WeClone 项目启动器")
    parser.add_argument("command", choices=[
        "api", "web", "train", "test", "data", "download", "check"
    ], help="要执行的命令")
    
    args = parser.parse_args()
    
    print("🎭 WeClone - 微信聊天记录克隆项目")
    print("=" * 50)
    
    if args.command == "api":
        start_api_service()
    elif args.command == "web":
        start_web_demo()
    elif args.command == "train":
        start_training()
    elif args.command == "test":
        test_model()
    elif args.command == "data":
        process_data()
    elif args.command == "download":
        download_model()
    elif args.command == "check":
        subprocess.run([sys.executable, "test_setup.py"])

if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("🎭 WeClone - 微信聊天记录克隆项目")
        print("=" * 50)
        print("使用方法:")
        print("  python start_weclone.py <command>")
        print("")
        print("可用命令:")
        print("  check    - 检查环境配置")
        print("  download - 下载模型")
        print("  data     - 处理聊天数据")
        print("  train    - 开始训练")
        print("  api      - 启动API服务")
        print("  web      - 启动Web界面")
        print("  test     - 测试模型")
        print("")
        print("示例:")
        print("  python start_weclone.py check")
        print("  python start_weclone.py api")
    else:
        main()
