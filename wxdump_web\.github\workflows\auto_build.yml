name: Build and Deploy Vue App

on:
  push:
    branches:
      - web

jobs:
  build:
    runs-on: windows-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v2

    - name: Set up Node.js
      uses: actions/setup-node@v2
      with:
        node-version: 20

    - name: Install dependencies
      run: npm install

    - name: Build the app
      run: npm run build

    - name: Cheak dist
      run: ls dist
