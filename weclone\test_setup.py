#!/usr/bin/env python3
"""
WeClone 项目设置测试脚本
用于验证环境配置是否正确
"""

import sys
import os
import json

def test_imports():
    """测试必要的包导入"""
    print("🔍 测试包导入...")
    
    try:
        import pandas as pd
        print("✅ pandas 导入成功")
    except ImportError as e:
        print(f"❌ pandas 导入失败: {e}")
        return False
    
    try:
        import transformers
        print("✅ transformers 导入成功")
    except ImportError as e:
        print(f"❌ transformers 导入失败: {e}")
        return False
    
    try:
        import llamafactory
        print("✅ llamafactory 导入成功")
    except ImportError as e:
        print(f"❌ llamafactory 导入失败: {e}")
        return False
    
    return True

def test_data_processing():
    """测试数据处理功能"""
    print("\n📊 测试数据处理...")
    
    # 检查示例数据
    if os.path.exists('./data/example_chat.csv'):
        print("✅ 示例数据文件存在")
    else:
        print("❌ 示例数据文件不存在")
        return False
    
    # 检查处理后的数据
    if os.path.exists('./data/res_csv/sft/sft-my.json'):
        print("✅ 处理后的训练数据存在")
        
        # 读取并显示数据统计
        with open('./data/res_csv/sft/sft-my.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
            print(f"📈 训练数据条数: {len(data)}")
            
            if len(data) > 0:
                print("📝 示例数据:")
                for i, item in enumerate(data[:2]):  # 显示前2条
                    print(f"  {i+1}. 指令: {item['instruction'][:50]}...")
                    print(f"     回答: {item['output'][:50]}...")
    else:
        print("❌ 处理后的训练数据不存在")
        return False
    
    return True

def test_config():
    """测试配置文件"""
    print("\n⚙️ 测试配置文件...")
    
    if os.path.exists('./settings.json'):
        print("✅ 配置文件存在")
        
        with open('./settings.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        model_path = config['common_args']['model_name_or_path']
        print(f"🤖 模型路径: {model_path}")
        
        if os.path.exists(model_path):
            print("✅ 模型目录存在")
        else:
            print("⚠️ 模型目录不存在，需要下载模型")
            
        return True
    else:
        print("❌ 配置文件不存在")
        return False

def test_template():
    """测试模板注册"""
    print("\n📋 测试模板注册...")
    
    try:
        sys.path.append('./src')
        from template import template_register
        template_register()
        print("✅ 模板注册成功")
        return True
    except Exception as e:
        print(f"❌ 模板注册失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 WeClone 项目设置测试")
    print("=" * 50)
    
    tests = [
        ("包导入", test_imports),
        ("数据处理", test_data_processing),
        ("配置文件", test_config),
        ("模板注册", test_template),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！项目设置正确。")
        print("\n📝 下一步:")
        print("1. 等待模型下载完成")
        print("2. 运行训练: python src/train_sft.py")
        print("3. 启动API服务: python src/api_service.py")
        print("4. 启动Web界面: python src/web_demo.py")
    else:
        print("⚠️ 部分测试失败，请检查环境配置。")
    
    return passed == total

if __name__ == "__main__":
    main()
